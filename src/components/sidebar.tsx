'use client';

import { useLogout } from '@/features/auth';
import { usePermissions } from '@/hooks/use-permissions';
import { useNavigationTranslations } from '@/hooks/use-translations';
import { usePathname } from '@/i18n/navigation';
import { MENU_ITEMS } from '@/lib/menu-items';
import { useProjectContext } from '@/providers/project-context';
import {
  ArrowLeft,
  BarChart3,
  Calendar,
  GalleryVerticalEnd,
  LogOut,
  MessageSquare,
  Shield,
  Users as UsersIcon,
} from 'lucide-react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

import { LanguageSwitcher } from '@/components/language-switcher';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarTrigger,
} from '@/components/ui/sidebar';

export function AppSidebar() {
  const pathname = usePathname();
  const router = useRouter();
  const logoutMutation = useLogout();
  const { hasPermission, userRole, isLoading, isContractor } = usePermissions();
  const { isInProjectContext, clearProject } = useProjectContext();
  const t = useNavigationTranslations();

  // Menu items for contractors when NOT in project context
  const contractorGeneralMenuItems = [
    {
      title: 'Projects',
      url: '/projects',
      icon: BarChart3,
      translationKey: 'projects',
    },
    {
      title: 'Profile',
      url: '/profile',
      icon: UsersIcon,
      translationKey: 'profile',
    },
  ];

  // Menu items for contractors when IN project context
  const contractorProjectMenuItems = [
    {
      title: 'Dashboard',
      url: '/dashboard',
      icon: BarChart3,
      translationKey: 'dashboard',
    },
    {
      title: 'PMAs',
      url: '/pmas',
      icon: Shield,
      translationKey: 'pmas',
    },
    {
      title: 'Maintenance Logs',
      url: '/maintenance-logs',
      icon: Calendar,
      translationKey: 'maintenanceLogs',
    },
    {
      title: 'Complaints',
      url: '/complaints',
      icon: MessageSquare,
      translationKey: 'complaints',
    },
    {
      title: 'Members',
      url: '/members',
      icon: UsersIcon,
      translationKey: 'members',
    },
  ];

  // Filter menu items based on user permissions (for non-contractors)
  const visibleMenuItems = MENU_ITEMS.filter((item) => {
    return hasPermission(item.permission);
  });

  // Determine which menu items to show
  const getCurrentMenuItems = () => {
    if (isContractor) {
      return isInProjectContext ? contractorProjectMenuItems : contractorGeneralMenuItems;
    }
    return visibleMenuItems;
  };

  const currentMenuItems = getCurrentMenuItems();

  const handleLogout = async () => {
    try {
      await logoutMutation.mutateAsync();
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const isActive = (url: string) => {
    // Remove locale prefix from pathname for comparison
    const pathWithoutLocale = pathname.replace(/^\/(en|ms)/, '') || '/';

    if (url === '/projects') {
      return pathWithoutLocale === '/projects' || pathWithoutLocale === '/';
    }
    if (url === '/dashboard') {
      return pathWithoutLocale === '/dashboard';
    }
    // For other routes, check if the current pathname starts with the menu item URL
    // This handles nested routes like /projects/create, /profile/settings, etc.
    return pathWithoutLocale.startsWith(url);
  };

  return (
    <Sidebar
      collapsible="icon"
      className="border-r transition-all duration-300"
    >
      <SidebarHeader className="border-b transition-all duration-300 group-data-[collapsible=icon]:px-2 group-data-[collapsible=icon]:py-3 p-4">
        <div className="flex items-center justify-between w-full">
          {/* Logo and title section */}
          <div className="flex items-center gap-3 group-data-[collapsible=icon]:hidden">
            <div className="bg-primary text-primary-foreground flex size-8 items-center justify-center rounded-lg shadow-sm transition-all duration-300">
              <GalleryVerticalEnd className="size-4 transition-all duration-300" />
            </div>
            <div className="transition-all duration-300">
              <h1 className="text-lg font-semibold">SimPLE</h1>
              <p className="text-xs text-muted-foreground">
                {isInProjectContext
                  ? 'Project View'
                  : userRole
                    ? `${t('professionalEdition')}`
                    : t('professionalEdition')}
              </p>
            </div>
          </div>

          {/* Collapse trigger - always visible */}
          <div className="group-data-[collapsible=icon]:flex group-data-[collapsible=icon]:justify-center group-data-[collapsible=icon]:w-full">
            <SidebarTrigger className="h-7 w-7 transition-all duration-300" />
          </div>
        </div>

        {/* Back to Projects button when in project context */}
        {isInProjectContext && (
          <div className="mt-3 pt-3 border-t">
            <button
              onClick={async () => {
                router.push('/projects');
                clearProject();
              }}
              className="flex items-center gap-2 text-xs text-muted-foreground hover:text-foreground transition-colors w-full"
            >
              <ArrowLeft className="h-3 w-3" />
              Back to Projects
            </button>
          </div>
        )}
      </SidebarHeader>
      <SidebarContent className="p-2 transition-all duration-300">
        <SidebarGroup>
          <SidebarGroupLabel className="text-xs font-medium text-muted-foreground px-2 mb-1 group-data-[collapsible=icon]:hidden transition-all duration-300">
            {t('navigation')}
          </SidebarGroupLabel>
          <SidebarGroupContent>
            {isLoading ? (
              <div className="px-3 py-2 text-sm text-muted-foreground group-data-[collapsible=icon]:px-0 group-data-[collapsible=icon]:text-center">
                <div className="group-data-[collapsible=icon]:hidden">
                  {t('loadingMenu')}
                </div>
                <div className="hidden group-data-[collapsible=icon]:block">
                  <div className="animate-pulse size-4 bg-muted rounded mx-auto"></div>
                </div>
              </div>
            ) : (
              <SidebarMenu className="space-y-1 group-data-[collapsible=icon]:space-y-2 group-data-[collapsible=icon]:flex group-data-[collapsible=icon]:flex-col group-data-[collapsible=icon]:items-center">
                {currentMenuItems.map((item) => {
                  const active = isActive(item.url);
                  const IconComponent = item.icon;
                  const titleKey =
                    'translationKey' in item ? item.translationKey : item.title;

                  return (
                    <SidebarMenuItem
                      key={item.title}
                      className="group-data-[collapsible=icon]:w-7"
                    >
                      <SidebarMenuButton
                        asChild
                        isActive={active}
                        tooltip={t(titleKey as keyof typeof t)}
                        className={`
                        group relative transition-all duration-300 ease-in-out
                        group-data-[collapsible=icon]:justify-center group-data-[collapsible=icon]:px-0 group-data-[collapsible=icon]:w-7 group-data-[collapsible=icon]:h-7 group-data-[collapsible=icon]:!p-0
                        ${active
                            ? 'bg-primary/10 text-primary font-medium group-data-[collapsible=icon]:bg-primary group-data-[collapsible=icon]:text-primary-foreground group-data-[collapsible=icon]:shadow-md border-l-3 border-l-primary group-data-[collapsible=icon]:border-l-0 ml-1 group-data-[collapsible=icon]:ml-0 group-data-[collapsible=icon]:rounded-lg'
                            : 'hover:bg-accent hover:text-accent-foreground group-data-[collapsible=icon]:hover:bg-primary/20 group-data-[collapsible=icon]:hover:scale-105 group-data-[collapsible=icon]:hover:shadow-md group-data-[collapsible=icon]:hover:rounded-lg'
                          }
                      `}
                      >
                        <Link
                          href={item.url}
                          className="flex items-center gap-3 w-full px-3 py-2 group-data-[collapsible=icon]:px-0 group-data-[collapsible=icon]:py-0 group-data-[collapsible=icon]:justify-center group-data-[collapsible=icon]:items-center group-data-[collapsible=icon]:w-7 group-data-[collapsible=icon]:h-7 transition-all duration-300"
                        >
                          <IconComponent
                            className={`
                          size-4 transition-all duration-300 group-data-[collapsible=icon]:size-4
                          ${active
                                ? 'text-primary group-data-[collapsible=icon]:text-white group-data-[collapsible=icon]:drop-shadow-sm'
                                : 'text-muted-foreground hover:text-accent-foreground group-data-[collapsible=icon]:hover:text-primary'
                              }
                        `}
                          />
                          <span
                            className={`
                          text-sm transition-all duration-300 group-data-[collapsible=icon]:hidden
                          ${active ? 'text-primary font-medium' : 'hover:text-accent-foreground'}
                        `}
                          >
                            {t(titleKey as keyof typeof t)}
                          </span>
                        </Link>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  );
                })}
              </SidebarMenu>
            )}
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter className="border-t p-2 transition-all duration-300">
        {/* Language Switcher */}
        <div className="group-data-[collapsible=icon]:hidden mb-2">
          <LanguageSwitcher />
        </div>

        <SidebarMenu className="group-data-[collapsible=icon]:flex group-data-[collapsible=icon]:justify-center">
          <SidebarMenuItem className="group-data-[collapsible=icon]:w-7">
            <SidebarMenuButton
              onClick={handleLogout}
              disabled={logoutMutation.isPending}
              tooltip={
                logoutMutation.isPending ? t('signingOut') : t('signOut')
              }
              className="group transition-all duration-300 hover:bg-destructive/10 hover:text-destructive disabled:opacity-50 group-data-[collapsible=icon]:justify-center group-data-[collapsible=icon]:px-0 group-data-[collapsible=icon]:w-7 group-data-[collapsible=icon]:h-7 group-data-[collapsible=icon]:!p-0 group-data-[collapsible=icon]:hover:scale-105 group-data-[collapsible=icon]:hover:shadow-md group-data-[collapsible=icon]:hover:rounded-lg"
            >
              <LogOut className="size-4 transition-all duration-300 group-data-[collapsible=icon]:size-4" />
              <span className="text-sm group-data-[collapsible=icon]:hidden transition-all duration-300">
                {logoutMutation.isPending ? t('signingOut') : t('signOut')}
              </span>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  );
}
